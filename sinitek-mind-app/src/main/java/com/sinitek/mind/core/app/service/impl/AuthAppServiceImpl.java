package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class AuthAppServiceImpl implements IAuthAppService {

    private final IAuthService authService;

    private final ITeamMemberService memberService;

    private final AppRepository appRepository;

    private final IPermissionService permissionService;

    @Override
    public AuthAppDTO authApp(String appId, long per) {

        if (StringUtils.isBlank(appId)) {
            throw new BussinessException("校验app时appId不能为空");
        }

        AuthDTO authDTO = authService.authCert();
        String tmbId = authDTO.getTmbId();

        AppDetailType appDetailType = authAppByTmbId(appId, tmbId, per, authDTO.getIsRoot());

        AuthAppDTO authAppDTO = new AuthAppDTO();
        BeanUtils.copyProperties(authDTO, authAppDTO);

        // 设置结果
        authAppDTO.setPermission(appDetailType.getPermission());
        authAppDTO.setApp(appDetailType);
        return authAppDTO;
    }

    @Override
    public AppDetailType authAppByTmbId(String appId, String tmbId, long per, boolean isRoot) {
        TeamMemberDTO teamMemberDTO = memberService.getById(tmbId);

        if (Objects.isNull(teamMemberDTO)) {
            throw new BussinessException("找不到对应成员信息");
        }

        String teamId = teamMemberDTO.getTeamId();
        PermissionDTO tmbPer = teamMemberDTO.getPermission();

        AppDetailType appDetailType = new AppDetailType();

        App app = appRepository.findById(appId).orElse(null);


        if (Objects.isNull(app)) {
            throw new BussinessException("对应App不存在");
        }

        // 设置app的属性
        BeanUtils.copyProperties(app, appDetailType);

        if (isRoot) {
            PermissionDTO permissionDTO = new PermissionDTO();
            permissionDTO.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
            appDetailType.setPermission(permissionDTO);
            return appDetailType;
        }

        if (!Objects.equals(teamId, app.getTeamId())) {
            throw new BussinessException("未授权的App，拒绝访问");
        }

        boolean isOwner = tmbPer.getIsOwner() || Objects.equals(app.getTmbId(), tmbId);

        PermissionDTO permission = new PermissionDTO();
        if (isOwner) {
            permission.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
        }

        List<String> appFolderTypeList = List.of(AppTypeEnum.FOLDER.getValue(), AppTypeEnum.HTTP_PLUGIN.getValue());
        if (appFolderTypeList.contains(app.getType()) || !app.getInheritPermission() || Objects.isNull(app.getParentId())) {
            // 1. is a folder. (Folders have compeletely permission)
            // 2. inheritPermission is false.
            // 3. is root folder/app.
            PermissionDTO rp = permissionService.getPermissionResultDTO(tmbId, appId, ResourceTypeEnum.APP.getValue());
            if (!Objects.isNull(rp)) {
                permission.setValue(rp.getValue());
            }
        } else {
            // is not folder and inheritPermission is true and is not root folder.
            AppDetailType parentAppDetailType = authAppByTmbId(tmbId, app.getParentId(), per, false);
            permission.setValue(parentAppDetailType.getPermission().getValue());
        }

        if (!permission.checkPer(per)) {
            throw new BussinessException("未授权的App，拒绝访问");
        }

        return appDetailType;
    }


}
