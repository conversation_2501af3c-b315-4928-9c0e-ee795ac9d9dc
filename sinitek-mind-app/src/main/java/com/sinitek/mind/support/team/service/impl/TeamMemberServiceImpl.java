package com.sinitek.mind.support.team.service.impl;

import cn.hutool.core.map.MapUtil;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.common.util.PageUtil;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.TeamPermissionDTO;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.*;
import com.sinitek.mind.support.team.enumerate.MemberStatusEnum;
import com.sinitek.mind.support.team.service.IDeptService;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import com.sinitek.mind.support.team.util.TeamMemberTransformerUtil;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.org.dto.EmployeeSearchDTO;
import com.sinitek.sirm.org.dto.UserInfoDetailedDataDTO;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.entity.Position;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.org.service.IOrgUpdaterService;
import com.sinitek.sirm.user.service.IUserExtService;
import com.sinitek.sirm.user.service.IUserService;
import com.sinitek.spirit.org.core.IOrgFinder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 团队成员服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeamMemberServiceImpl implements ITeamMemberService {

    /**
     * FastGPT中的概念于SirmApp中的概念对应：
     * 成员-员工
     * 部门-组织结构
     * 群组-小组
     */

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IOrgFinder orgFinder;

    @Autowired
    private IOrgUpdaterService orgUpdaterService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IUserExtService userExtService;

    @Autowired
    private IAccountService accountService;

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private IDeptService deptService;

    @Override
    public PageResult<TeamMemberDTO> getTeamMemberList(TeamMemberListRequest request) {
        String status = request.getStatus();
        Boolean withOrgs = request.getWithOrgs();
        Boolean withPermission = request.getWithPermission();
        String searchKey = request.getSearchKey();
        String groupId = request.getGroupId();

        if (Boolean.FALSE.equals(withOrgs)) {
            // 只查询部门成员，只是部门的直属成员
            return getTeamMemberListByOrgId(request);
        }

        EmployeeSearchDTO employeeSearchDTO = new EmployeeSearchDTO();
        if (StringUtils.isNotBlank(searchKey)) {
            employeeSearchDTO.setUserName(searchKey);
        }
        if (StringUtils.isNotBlank(status) && StringUtils.isNotBlank(TeamMemberTransformerUtil.status2app(status))) {
            employeeSearchDTO.setInservice(TeamMemberTransformerUtil.status2app(status));
        }
        if (StringUtils.isNotBlank(groupId)) {
            // 群组查询
            employeeSearchDTO.setOrgType(1);
            employeeSearchDTO.setUnitIds(List.of(groupId));
        }
        Tuple2<Integer, Integer> pageInfo = PageUtil.formatPageOffset(request);
        employeeSearchDTO.setPageIndex(pageInfo.getT1());
        employeeSearchDTO.setPageSize(pageInfo.getT2());

        TableResult<UserInfoDetailedDataDTO> empTableResult = orgService.findEmployeeList(employeeSearchDTO);
        List<UserInfoDetailedDataDTO> empList = empTableResult.getData().getDatalist();

        PageResult<TeamMemberDTO> result = new PageResult<>();
        result.setTotal(empTableResult.getData().getTotalsize());
        result.setList(empList.stream()
            .map(this::convertUserInfoDetailedDataDTO2TeamMemberDTO)
            .collect(Collectors.toList()));

        if (Boolean.TRUE.equals(withOrgs)) {
            // 构建成员的组织信息
            result.getList().forEach(dto -> {
                List<String> deptNameList = deptService.getDeptNameByTmbId(dto.getTmbId());
                dto.setOrgs(deptNameList.stream()
                    // 添加一个前缀，FastGPT前端需要
                    .map(str -> "/" + str)
                    .toList());
            });
        }
        if (Boolean.TRUE.equals(withPermission)) {
            // 构建成员的权限信息
            List<String> tmbIdList = result.getList().stream()
                .map(TeamMemberDTO::getTmbId)
                .toList();
            Map<String, TeamPermissionDTO> perMap = permissionService.getTeamPermissionResultDTO(
                tmbIdList);
            result.getList().forEach(dto -> {
                TeamPermissionDTO perDTO = MapUtil.get(perMap, dto.getTmbId(),
                    TeamPermissionDTO.class, new TeamPermissionDTO());
                dto.setPermission(perDTO);
            });
        }

        return result;
    }

    /**
     * 查询部门下的成员，只查询这一层级的
     * 步骤：查出指定部门的所有岗位，找到所有岗位下的所有人员
     */
    private PageResult<TeamMemberDTO> getTeamMemberListByOrgId(TeamMemberListRequest request) {
        PageResult<TeamMemberDTO> result = new PageResult<>();
        if (StringUtils.isBlank(request.getOrgId())) {
            // FastGPT传入的orgId为空，表示是根组织，但是框架的根组织中是不需要直接挂载岗位的，所以直接返回空
            LinkedList<TeamMemberDTO> list = new LinkedList<>();
            result.setList(list);
            result.setTotal(0);
            return result;
        }
        List<Position> positions = orgService.findPositionsByUnitId(request.getOrgId());
        List<TeamMemberDTO> allEmp = positions.stream()
            .flatMap(position -> orgService.findEmployeeByPosId(position.getOrgid()).stream())
            .collect(Collectors.toSet())
            .stream()
            .map(this::convertEmployee2TeamMemberDTO)
            .toList();

        result.setTotal(allEmp.size());
        result.setList(PageUtil.subList(allEmp, request));

        if (Boolean.TRUE.equals(request.getWithPermission())) {
            // 构建成员的权限信息
            List<String> tmbIdList = result.getList().stream()
                .map(TeamMemberDTO::getTmbId)
                .toList();
            Map<String, TeamPermissionDTO> perMap = permissionService.getTeamPermissionResultDTO(
                tmbIdList);
            result.getList().forEach(dto -> {
                TeamPermissionDTO perDTO = MapUtil.get(perMap, dto.getTmbId(),
                    TeamPermissionDTO.class, new TeamPermissionDTO());
                // 如果员工的角色是管理员，则认为是团队的拥有者
                Boolean isAdmin = orgService.isAdmin(dto.getTmbId());
                if (isAdmin) {
                    // 设置所有权限
                    perDTO.setValue(PermissionConstant.OWNER_PERMISSION_VAL);
                }
                dto.setPermission(perDTO);
            });
        }

        return result;
    }


    @Override
    public TeamMemberCountResponse getTeamMemberCount() {
        TeamMemberCountResponse response = new TeamMemberCountResponse();
        response.setCount(orgService.findAllEmployees().size());
        return response;
    }
    
    @Override
    public void updateMemberNameByManager(UpdateMemberNameByManagerRequest request) {
        Employee employee = orgService.getEmployeeById(request.getTmbId());
        if (employee == null) {
            throw new RuntimeException("该成员不存在");
        }
        employee.setEmpName(request.getName());
        orgUpdaterService.updateEmployee(employee);
    }
    
    @Override
    public void updateMemberName(UpdateMemberNameRequest request) {
        String orgId = CurrentUserFactory.getOrgId();
        Employee employee = orgService.getEmployeeById(orgId);
        if (employee == null) {
            throw new RuntimeException("该成员不存在");
        }
        employee.setEmpName(request.getName());
        orgUpdaterService.updateEmployee(employee);
    }
    
    @Override
    public void deleteMember(String tmbId) {
        // 离职，由团队管理进行离职成员,接收的tmdId为离职的成员id
        userService.batchLeaveWithoutUnfinishTask(new String[]{tmbId});
    }
    
    @Override
    public void updateInviteStatus(UpdateInviteStatusRequest request) {
        // 未在界面找到对应按钮
    }
    
    @Override
    public void restoreMember(RestoreMemberRequest request) {
        // 未在界面找到对应按钮
        // 入职，将成员状态恢复为在职
        userService.batchInservice(new String[]{request.getTmbId()});
    }
    
    @Override
    public void leaveTeam() {
        // 也是离职，用户主动点击离开团队按钮
        String orgId = CurrentUserFactory.getOrgId();
        userService.batchLeaveWithoutUnfinishTask(new String[]{orgId});
    }

    @Override
    public TeamMemberDTO getById(String tmbId) {
        Employee employee = orgService.getEmployeeById(tmbId);
        if (employee == null) {
            throw new RuntimeException("该成员不存在");
        }
        return convertEmployee2TeamMemberDTO(employee);
    }

    private TeamMemberDTO convertUserInfoDetailedDataDTO2TeamMemberDTO(UserInfoDetailedDataDTO dto) {
        TeamMemberDTO teamMemberDTO = new TeamMemberDTO();
        teamMemberDTO.setUserId(dto.getUserId());
        teamMemberDTO.setTmbId(dto.getOrgid());
        teamMemberDTO.setTeamId(dto.getTenantId());
        teamMemberDTO.setMemberName(dto.getEmpname());
        teamMemberDTO.setAvatar(accountService.getAvatarByOrgId(dto.getOrgid()));
        setTeamMemberRole(teamMemberDTO, dto.getOrgid());
        if (dto.getInservice()) {
            // 在职
            teamMemberDTO.setStatus(MemberStatusEnum.ACTIVE.getGptValue());
        } else {
            // 离职
            teamMemberDTO.setStatus(MemberStatusEnum.INACTIVE.getGptValue());
        }
        Employee employee = orgService.getEmployeeById(dto.getOrgid());
        // 电话或邮箱接收信息
        teamMemberDTO.setContact(getContact(employee));
        teamMemberDTO.setCreateTime(employee.getCreateTimeStamp());
        teamMemberDTO.setUpdateTime(employee.getUpdateTimeStamp());

        return teamMemberDTO;
    }

    /**
     * 设置成员的角色
     * @param teamMemberDTO
     * @param orgId
     */
    private void setTeamMemberRole(TeamMemberDTO teamMemberDTO, String orgId) {
        // 判断是否为管理员
        if (orgService.isAdmin(orgId)) {
            // FastGpt中只有这一种角色，其他的为空
            teamMemberDTO.setRole("owner");
        } else {
            teamMemberDTO.setRole("");
        }
    }

    private TeamMemberDTO convertEmployee2TeamMemberDTO(Employee employee) {
        TeamMemberDTO teamMemberDTO = new TeamMemberDTO();
        teamMemberDTO.setUserId(employee.getUserId());
        teamMemberDTO.setTmbId(employee.getId());
        teamMemberDTO.setTeamId(employee.getTenantId());
        teamMemberDTO.setMemberName(employee.getEmpName());
        teamMemberDTO.setAvatar(accountService.getAvatarByOrgId(employee.getId()));
        setTeamMemberRole(teamMemberDTO, employee.getId());
        if (employee.getInservice() == 1) {
            // 在职
            teamMemberDTO.setStatus(MemberStatusEnum.ACTIVE.getGptValue());
        } else {
            // 离职
            teamMemberDTO.setStatus(MemberStatusEnum.INACTIVE.getGptValue());
        }
        teamMemberDTO.setContact(getContact(employee));
        teamMemberDTO.setCreateTime(employee.getCreateTimeStamp());
        teamMemberDTO.setUpdateTime(employee.getUpdateTimeStamp());

        return teamMemberDTO;
    }

    /**
     * 获取成员的联系方式，优先办公电话
     * @param employee 员工
     * @return 联系方式
     */
    private String getContact(Employee employee) {
        // 需要进行解密操作
        String tel = employee.getTel();
        if (StringUtils.isNotBlank(tel)) {
            return tel;
        }
        return employee.getEmail();
    }
} 